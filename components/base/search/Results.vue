<template>
	<slot :items="getSearchResults" :searchTerm="searchTerm" :searchContent="searchContent" :loading="loading" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const config = useAppConfig();
	const catalog = useCatalog();
	const publish = usePublish();
	const pages = usePage();
	const labels = useLabels();
	const {getAppUrl} = useApiRoutes();
	const {searchResults, getSearchResults, setSearchProductsCount} = useSearch();

	const props = defineProps({
		searchModules: Array, // [{module: 'catalog', title: 'Module title'}]
	});

	const searchTerm = ref(route.query.search_q);
	const searchContent = computed(() => route.query.search_content);
	const loading = ref(false);

	// inject data provided by parent Category component. If no data is provided, set category to null
	const {category} = inject('baseCatalogCategoryData', {category: null});

	// Set catalog fetch options (used only if default tab is not products)
	let catalogFetchOptions = {};
	catalogFetchOptions.mode = 'index';
	if (config.catalog?.productsResponseFields?.length) catalogFetchOptions.response_fields = config.catalog.productsResponseFields;
	catalogFetchOptions.search_q = searchTerm.value?.trim();
	catalogFetchOptions._search_id = true;
	if (category?.value?.position_h) catalogFetchOptions.category_position = category.value.position_h;

	// run all search modules
	async function search() {
		if (!props.searchModules || !route.query.search_q) return;

		loading.value = true;
		const results = {};

		for (const item of props.searchModules) {
			const searchModule = item.module.split('|');

			let moduleTitle = item.title;
			if (!moduleTitle && searchModule[1]) moduleTitle = labels.get('search_' + searchModule[0] + '_' + searchModule[1]);
			if (!moduleTitle) moduleTitle = labels.get('search_' + searchModule[0]);

			if (searchModule[0] == 'cms') {
				const pagesRes = await pages.fetch({mode: 'search', search_q: searchTerm.value?.trim()});
				results['cms'] = {
					id: 'cms',
					title: moduleTitle,
					url: `${getAppUrl('search')}?search_q=${route.query.search_q}&search_content=${searchModule[0]}`,
					total: pagesRes?.meta_data?.items_total || 0,
				};
			}

			if (searchModule[0] == 'publish') {
				const cat = searchModule[1] ? {category_position: [searchModule[1]]} : {};
				const publishKey = searchModule[1] ? 'publish.' + searchModule[1] : 'publish';
				const publishRes = await publish.fetchPosts({search_q: searchTerm.value?.trim(), limit: 1, mode: 'search', response_fields: ['id'], ...cat});

				results[publishKey] = {
					id: publishKey,
					title: moduleTitle,
					url: `${getAppUrl('search')}?search_q=${route.query.search_q}&search_content=${publishKey}`,
					total: publishRes?.data?.meta_data?.items_total || 0,
				};
			}

			if (searchModule[0] == 'catalog') {
				if (searchModule[1]) catalogFetchOptions.category_position = searchModule[1];
				results['catalog'] = {
					id: 'catalog',
					title: moduleTitle,
					url: `${getAppUrl('catalog')}?search_q=${route.query.search_q}`,
					total: 0, // Use pagination directly
				};
			}
		}

		searchResults.value = results;
		loading.value = false;
	}

	onMounted(async () => {
		if (!getSearchResults.value) {
			await search();

			// Fetch products counter if default tab is not products
			if (route.query.search_content) {
				loading.value = true;
				const productsRes = await catalog.fetchProducts(catalogFetchOptions);
				setSearchProductsCount(productsRes?.data?.meta_data?.items_total || 0);
				loading.value = false;
			}
		}
	});

	// watch for search term change
	watch(
		() => route.query.search_q,
		async (newData, oldData) => {
			await new Promise(resolve => setTimeout(resolve, 500));
			searchTerm.value = route.query.search_q;
			if (route.meta.contentType === 'search' && !loading.value) {
				await search();
			}
		}
	);

	// provide data to child components
	provide('baseSearchResultsData', {
		searchTerm,
		loading,
	});
</script>
