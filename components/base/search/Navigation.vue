<template>
	<slot :items="getSearchResults" :selected="selected" :loading="loading" />
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const route = nuxtApp._route;
	const props = defineProps({
		redirectToResults: {
			type: Boolean,
			default: true,
		},
	});
	const selected = computed(() => (route.query.search_content ? route.query.search_content : 'catalog'));
	const {getSearchResults, searchProductsCount} = useSearch();
	const {loading: loadingProducts} = inject('baseCatalogProductsData', {loading: false});
	const {loading: loadingSearch} = inject('baseSearchResultsData', {loading: false});
	const loading = computed(() => loadingProducts.value || loadingSearch.value);

	onMounted(() => {
		if (props.redirectToResults) {
			let redirectTimeout;
			watch(
				searchProductsCount,
				async (newData, oldData) => {
					if (redirectTimeout) clearTimeout(redirectTimeout);
					if (newData == oldData || route.query.search_content) return;

					redirectTimeout = setTimeout(async () => {
						if (!newData) {
							const pageWithResults = Object.values(getSearchResults.value).find(el => el.total > 0);
							if (pageWithResults) await navigateTo(pageWithResults.url);
						}
					}, 1000);
				},
				{immediate: true}
			);
		}
	});
</script>
