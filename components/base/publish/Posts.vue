<template>
	<slot :items="posts" :load-more="loadMore" :pagination="pagination" :next-page="pagination.page?.next" :loading="loading" :featuredPosts="featuredPosts" :searchFields="searchFields" :searchFieldsMeta="searchFieldsMeta" />
</template>

<script setup>
	const config = useAppConfig();
	const nuxtApp = useNuxtApp();
	const router = useRouter();
	const route = nuxtApp._route;
	const emit = defineEmits(['load', 'filterPosts']);
	const {bus} = useEventBus();
	const {fetchPosts} = usePublish();
	const {generateThumbs} = useImages();
	const {getLastUrlSegment} = useUrl();
	const {getLastArrayElement} = useArrayUtils();
	const {onElementVisibility, onScroll} = useDom();

	const props = defineProps({
		fetch: Object,
		thumbPreset: String,
		featuredPosts: [String, Number],
		featuredThumbPreset: String,
		infiniteScroll: Number,
		responseFields: Array,
	});

	const posts = ref([]);
	const searchFields = shallowRef([]);
	const searchFieldsMeta = shallowRef({});
	const loading = ref(1);
	const pagination = ref({});
	let fetchOptions = {};

	// inject data provided by parent Category component. If no data is provided, set category to null
	const {category, contentType} = inject('basePublishCategoryData', {category: null, contentType: null});

	console.log(contentType.value);

	// set fetch options
	_setFetchOptions();

	onMounted(() => {
		// listen for back navigation
		window.addEventListener('popstate', handleBackNavigation);
	});

	// listen for back/forward navigation and refresh posts
	let isPopState = false;
	function handleBackNavigation() {
		isPopState = true;
	}
	router.afterEach(to => {
		if (isPopState) {
			emitGlobal('publishPostsUpdate', {
				page: to.query?.page || null,
				toPage: to.query?.to_page || null,
			});
			isPopState = false;
		}
	});

	// get initial posts
	const postsData = await fetchPosts(fetchOptions);
	posts.value = postsData?.data?.items?.length ? [...postsData.data.items] : [];
	searchFields.value = postsData?.data?.search_fields || [];
	searchFieldsMeta.value = postsData?.data?.search_fields_meta_data || {};

	// update pagination data for initial posts
	if (postsData?.data?.meta_data) {
		updatePagination(postsData.data);
	}

	// if provided page is greater than total pages, redirect to last page
	if (pagination.value?.page?.to_page > pagination.value?.page?.total) {
		await navigateTo({query: {...route.query, 'to_page': undefined}});
	}

	// generate thumbs
	if (props.thumbPreset) {
		await generateThumbs({
			data: posts.value,
			preset: props.thumbPreset,
		});
	}

	// featured posts
	const featuredPosts = ref({});
	if (props.featuredPosts) {
		featuredPosts.value = posts.value.slice(0, props.featuredPosts);
		posts.value = posts.value.slice(props.featuredPosts);

		// generate thumbs for featured posts
		if (props.featuredThumbPreset) {
			await generateThumbs({
				data: featuredPosts.value,
				preset: props.featuredThumbPreset,
			});
		}
	}

	// finish loading
	loading.value = 0;

	// load more posts
	async function loadMore() {
		// do not load more posts if next page is not available
		if (!pagination.value.page.next) {
			return false;
		}

		// start loading
		loading.value = 1;

		// fetch new posts
		const newPosts = await fetchPosts({...fetchOptions, page: pagination.value.page.next});
		let appendPosts = newPosts?.data?.items?.length ? newPosts.data.items : [];

		// generate thumbs for new posts
		if (props.thumbPreset && appendPosts.length) {
			await generateThumbs({
				data: appendPosts,
				preset: props.thumbPreset,
			});
		}

		// push new posts
		posts.value.push(...appendPosts);

		// update to_page route param
		await navigateTo({query: {...route.query, 'to_page': pagination.value.page.next}});

		emit('load', {items: appendPosts});

		// update pagination data
		updatePagination(newPosts?.data);

		// finish loading
		loading.value = 0;
	}

	const infiniteScroll = props.infiniteScroll ? props.infiniteScroll : 0;
	if (infiniteScroll > 0) {
		let scrollDirection;
		let infiniteScrollTriggered = 0;
		const scrollTrigger = ref(null);

		onMounted(async () => {
			await nextTick();
			scrollTrigger.value = document.querySelector('[data-posts-scroll-trigger]'); // infinite scroll auto trigger
		});

		onScroll({
			debounce: 50,
			callback: ({direction}) => {
				scrollDirection = direction;
			},
		});

		onElementVisibility({
			target: scrollTrigger,
			callback: async ({isVisible}) => {
				if (isVisible && scrollDirection == 'down' && infiniteScrollTriggered < infiniteScroll) {
					await loadMore();
					infiniteScrollTriggered++;
				}
			},
		});
	}

	// watch for filter or pagination updates and fetch new products
	let filterTimeout;
	watch(
		() => bus.value,
		async () => {
			// do not fetch/filter posts if event is not "publishPostUpdate"
			if (bus.value.event != 'publishPostsUpdate') return false;

			// start loading
			loading.value = 1;

			filterTimeout = setTimeout(async () => {
				// set fetch options
				_setFetchOptions();

				// if bus data contains pagination data, use it
				const page = bus.value.data?.page ? bus.value.data.page : null;
				const toPage = bus.value.data?.toPage ? bus.value.data.toPage : null;

				// fetch posts
				const postsData = await fetchPosts({...fetchOptions, page: page, to_page: toPage});
				posts.value = postsData?.data?.items?.length ? postsData.data.items : [];
				searchFields.value = postsData?.data?.search_fields || [];
				searchFieldsMeta.value = postsData?.data?.search_fields_meta_data || {};

				// emit filter event with filtered posts
				emit('filterPosts', {items: posts.value});

				// update pagination data
				updatePagination(postsData?.data);

				// generate thumbs
				if (props.thumbPreset) {
					await generateThumbs({
						data: posts.value,
						preset: props.thumbPreset,
					});
				}

				// finish loading
				loading.value = 0;
			}, 100);
		}
	);

	// fetch options
	function _setFetchOptions() {
		// set initial fetch options
		fetchOptions = {
			...props.fetch,
			mode: 'index',
		};
		if (config?.publish?.postsResponseFields?.length && !fetchOptions.response_fields) fetchOptions.response_fields = config.publish.postsResponseFields;
		if (props.responseFields) fetchOptions.response_fields = props.responseFields;

		// set category options (used by backend)
		if (contentType.value == 'category') {
			//if (category.value?.position_h) fetchOptions.category_position = category.value.position_h;
			//if (category.value?.id && !category.value?.pageId) fetchOptions._category_id = category.value.id;
			fetchOptions.search_id = Number(category.value.search_id) || 1;
		}

		if (category?.value?.code) fetchOptions.category_code = category.value.code;
		if (route.query?.search_q) {
			fetchOptions.search_q = route.query.search_q;
			fetchOptions.ignore_per_page_extracontent = true;
		}
		if (route.meta?.contentType == 'tag') {
			const routeSegments = getLastUrlSegment(route.path, {ignoreLang: true});
			const tagId = getLastArrayElement(routeSegments.split('-'));
			if (tagId) fetchOptions.tags_ids = tagId;
		}

		// restructure filter query params
		if (route.query) {
			Object.entries(route.query).forEach(([key, value]) => {
				if (value) {
					const formattedValue = Array.isArray(value) ? value.join(',') : value;
					fetchOptions[key] = formattedValue;
				}
			});
		}

		// if route query contains catalogcategory_id param string separated by comma, convert it to array
		if (route.query?.catalogcategory_id) {
			fetchOptions.catalogcategory_id = route.query.catalogcategory_id.split(',');
		}

		console.log(fetchOptions);
	}

	// update pagination data for initial posts
	function updatePagination(data) {
		pagination.value = data?.meta_data?.pagination ? data.meta_data.pagination : {};
		pagination.value.items.current = posts.value?.length || 0;
	}

	onBeforeUnmount(() => {
		clearTimeout(filterTimeout);
	});

	// provide data to child components
	provide('pagination', {pagination, mode: 'publish'});
	provide('basePublishPostsData', {searchFieldsMeta, searchFields, loading, pagination});

	// reset data on unmount
	onBeforeUnmount(() => {
		posts.value = [];
		searchFields.value = [];
		searchFieldsMeta.value = {};
		pagination.value = {};
	});
</script>
