<template>
	<BaseCatalogLists :fetch="{code: 'checkout_suggestion', use_total: true, cache_lifetime: 60, response_fields: ['code']}" v-slot="{items: list}">
		<BaseCatalogProductsWidget
			:fetch="{list_code: list[0].code, sort: 'cheaper', only_available: true, limit: 12, category_id: cart.product_categories}"
			v-slot="{items: products}"
			v-if="list?.length && toFree?.amount > 0 && cart?.product_categories?.length"
			:gtm-tracking="{item_list_id: 'checkout_suggestion', item_list_name: labels.get('free_delivery_widget_title')}"
			@loadProductsWidget="onLoadedProducts">
			<div class="cart_info_total_extra_shipping_to_free_box free-delivery-widget active" :class="[mode]">
				<BaseCmsLabel code="free_delivery_widget_title" class="fdw-title" tag="div" />
				<div class="fdw-note" v-html="labels.get('free_delivery_widget_note').replace('%FREE_ABOVE%', formatCurrency(toFree.above))"></div>

				<div class="fdw-container">
					<div class="slider-free-delivery">
						<CatalogSpeciallists :items="products" :perPage="3" />
					</div>
				</div>
			</div>
		</BaseCatalogProductsWidget>
	</BaseCatalogLists>
</template>

<script setup>
	const labels = useLabels();
	const {formatCurrency} = useCurrency();
	const props = defineProps(['toFree','mode','cart']);

	function onLoadedProducts(products){
		if(products?.items.length){
			sendProductImpressions(products.items, "cart_delivery");
		}
	}
</script>
