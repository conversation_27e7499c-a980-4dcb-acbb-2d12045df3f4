<template>
	<BaseWebshopCouponForm v-slot="{onSubmit, onRemove, couponCode, message, error, loading, activeCoupon, handleInput}" :mode="props.mode">
		<div class="ww-coupons" :class="[{'ww-coupons-preview': mode == 'preview'}, {'ww-auth-coupons': mode == 'auth'}, {'ww-coupons-checkout': mode == 'checkout'}, {'base-coupon-error': error}, {'active': activeCoupon}, {'loading': loading}]">
			<div v-if="activeCoupon" class="ww-coupons-active">
				<span class="ww-coupons-title" v-html="labels.get('coupon_included_code').replace('%COUPON_CODE%', '<strong>'+activeCoupon.code+'</strong>')"> </span>
				<span class="ww-coupon-delete" @click="onRemove"><BaseCmsLabel code="coupon_remove" tag="span" /></span>
			</div>

			<form v-else-if="(!activeCoupon || mode == 'auth')" class="ww-coupons-form" :class="mode == 'auth' && 'ww-coupons-form ww-auth-coupons-form'" @submit.prevent="onSubmit()">
				<template v-if="mode == 'preview'">
					<BaseCmsLabel code="coupon_have_coupon" class="ww-coupons-label ww-preview-coupons-label" tag="label" @click="previewCouponActive = !previewCouponActive" />
					<div class="ww-coupons-fields preview-coupon-container" v-if="previewCouponActive">
						<input id="coupon_code" :placeholder="labels.get('coupon_enter_code')" type="text" :value="couponCode" name="coupon_code" @keyup="handleInput" />
						<button class="ww-btn-add ww-coupons-add" type="submit"><BaseCmsLabel code="coupon_btn_add" tag="span" /></button>
						<BaseCmsLabel v-if="message" class="coupon_message coupon-message" tag="div" :code="message" />
					</div>
				</template>
				<template v-else>
					<BaseCmsLabel code="coupon_have_coupon" class="ww-coupons-label" tag="label" />
					<div :class="mode == 'auth' && 'ww-coupons-fields'">
						<input id="coupon_code" :placeholder="labels.get('coupon_enter_code')" type="text" :value="couponCode" name="coupon_code" @keyup="handleInput" class="ww-coupons-input" />
						<button class="ww-btn-add ww-coupons-add" type="submit"><BaseCmsLabel code="coupon_btn_add" tag="span" /></button>
						<BaseCmsLabel v-if="message" class="coupon_message" tag="div" :code="message" />
					</div>
				</template>
			</form>

			<template v-if="mode != 'auth'">
				<BaseAuthCoupons v-if="auth.isLoggedIn()" v-slot="{items, onActivate, status}">
					<div v-if="!activeCoupon && items?.length" class="ww-coupons-list">
						<BaseCmsLabel code="coupon_available" class="ww-coupons-list-title" tag="h5" />
						<table class="ww-coupons-table">
							<tr :class="{'active': !activeCoupon}" v-for="item in items" :key="item.id">
								<td class="col-code">{{item.code}}</td>
								<td class="col-type">
									<span v-if="item.type == 'f'"><BaseUtilsFormatCurrency :price="item.coupon_price" /></span>
									<span v-else>-{{ item.coupon_percent * 100 }}%</span>
								</td>
								<td class="col-link">
									<span class="btn-coupon-add" @click="onActivate(item.code)"><BaseCmsLabel code="coupon_use" /></span>
								</td>
							</tr>
						</table>
					</div>
					<BaseCmsLabel v-if="status" class="coupon_message" tag="div" :code="status.label_name" />
				</BaseAuthCoupons>
			</template>
		</div>
	</BaseWebshopCouponForm>
</template>

<script setup>
	const props = defineProps(['mode']);
	const labels = useLabels();
	const auth = useAuth();

	const previewCouponActive = ref(false);
</script>

<style lang="less" scoped>
	//.ww-preview-coupons{margin: 0;}
</style>
