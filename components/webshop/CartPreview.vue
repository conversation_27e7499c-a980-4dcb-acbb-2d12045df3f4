<template>
	<BaseWebshopCartPreview v-slot="{parcels, cartUrl}">
		<div class="ww-preview" :class="{'active': active}">
			<div class="ww-preview-header">
				<div class="ww-preview-title" :class="{'active': counter > 0}">
					<BaseCmsLabel code="in_cart" />
					<span class="ww-preview-count"
						>(<span class="cart_info_item_count">{{counter}}</span
						>)</span
					>
				</div>
				<slot name="closePreview" />
			</div>
			<div class="ww-preview-items-container" v-if="parcels[0]?.items?.length">
				<div class="ww-preview-items">
					<div class="ww-preview-table">
						<WebshopCartItemSmall v-for="item in parcels[0].items" :data="item" :key="item.shopping_cart_code" />
					</div>
				</div>
			</div>
			<div class="ww-preview-bottom">
				<WebshopCouponForm mode="preview" />
				<BaseWebshopPriorityOrder v-slot="{onSubmit, loading, isActive}">
					<div class="priority-order quick-priority-order" :class="[{'loading': loading}, props.mode]">
						<input type="checkbox" id="field-priority" :checked="isActive" @click.prevent="onSubmit" />

						<BaseCmsLabel for="field-priority" code="priority_order" tag="label" />
						<div v-if="loading" />
					</div>
				</BaseWebshopPriorityOrder>
				<WebshopTotal mode="preview" simple="true" />

				<!--  FIXME INTEG DK dodati obavijest o minimalnom iznosu za narudžbu -->
				<!-- Min order alert -->
				<!--
				<?php if (isset($shopping_cart['total_extra_shipping_min_total_error']) AND $shopping_cart['total_extra_shipping_min_total_error']): ?>
					<div class="ww-minprice minprice-tooltip" style="display:none;">
						<?php
						echo str_replace(array(
							'%TOTAL_MIN%',
							'%TOTAL_MISSING%',
						), array(
							Utils::currency_format($shopping_cart['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
							Utils::currency_format($shopping_cart['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
						), Arr::get($cmslabel, 'minimal_order_price_full'));

						?>
					</div>
				<?php endif; ?>
				-->

				<div class="ww-preview-buttons">
					<NuxtLink :to="cartUrl" class="btn btn-orange ww-btn-view">
						<BaseCmsLabel code="view_shopping_cart" tag="span" />
					</NuxtLink>
				</div>

				<BaseWebshopFreeShipping v-slot="{item}">
					<div class="free-delivery-container cart_info_total_extra_shipping_to_free_box" v-show="item?.amount != 0">
						<div class="free-delivery-title cart_info_total_extra_shipping_to_free_box">
							<div class="free-delivery">
								<BaseCmsLabel code="min_total_missing" tag="span" /> <strong class="w-missing-shipping-value cart_info_total_extra_shipping_to_free"><BaseUtilsFormatCurrency :price="item.amount" /></strong>
							</div>
						</div>

						<div class="free-delivery-scale" v-show="item.amount != 0">
							<div class="free-delivery-scale-amount">
								<span class="cart_info_total_items"><BaseUtilsFormatCurrency :price="parcels[0].total.total" /></span> /
								<span class="cart_info_total_extra_shipping_free_above"><BaseUtilsFormatCurrency :price="item.above" /></span>
							</div>
							<div class="free-delivery-scale-bar cart_info_total_extra_shipping_to_free_percent" :style="'width: '+item.percent"></div>
						</div>
					</div>
				</BaseWebshopFreeShipping>
			</div>
		</div>
	</BaseWebshopCartPreview>
</template>

<script setup>
	const props = defineProps(['active', 'counter']);
</script>
