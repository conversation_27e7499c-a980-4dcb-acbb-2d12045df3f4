<template>
	<div class="wp" :class="{'wp-checkout': mode == 'checkout'}" :id="'product_details_'+data.shopping_cart_code">
		<BaseWebshopRemoveProduct :item="data" v-slot="{onRemove, loading}">
			<BaseCatalogWishlist v-slot="{items: wishlistItems}">
				<div class="wp-image">
					<figure>
						<NuxtLink :to="data.item.url_without_domain">
							<BaseUiImage v-if="mode == 'checkout'" loading="lazy" :data="data.item?.image_thumbs?.['width120-height120']" default="/images/no-image-120.jpg" :alt="data.item.title" />
							<BaseUiImage v-else loading="lazy" :data="data.item?.image_thumbs?.['width70-height70']" default="/images/no-image-50.jpg" :alt="data.item.title" />
						</NuxtLink>
					</figure>
				</div>

				<div class="wp-content">
					<div class="wp-cnt">
						<div class="wp-title">
							<NuxtLink :to="data.item.url_without_domain">{{ data.item.title }}</NuxtLink>
						</div>
						<!-- FIXME INTEG - tu postoji provjera vezana za 'product_code' koji ne postoji kao paramtera, šta ćemo s tim? <?php if(!empty($product_data['product_code'])): ?><div class="wp-code"><?php echo Arr::get($cmslabel, 'code'); ?>: <?php echo $product_data['product_code']; ?></div><?php endif; ?> -->
						<div class="wp-code"><BaseCmsLabel code="code" />: {{data.item.code}}</div>
						<!--  <?php if (!empty($pickup_only)): ?>
								<?php
								$pickup_available_count = count(Arr::get($product_status, 'warehouses', []));
								$locations = Widget_Location::points(['lang' => $info['lang'], 'available_pickup' => TRUE]);
								if (!empty($locations)) {
									$location_titles = '';
									$location_ids = array_keys(Arr::get($product_status, 'warehouses', []));
									$last_key = end($location_ids);
									foreach ($locations as $location_id => $location_data) {
										if (in_array($location_id, array_keys(Arr::get($product_status, 'warehouses', [])))) {
											$location_titles .= (($location_id == $last_key) ? $location_data['title'] : $location_data['title'] . ', ');
										}
									}
								}
								?>
								<span class="cp-badge wp-badge"><?php echo Arr::get($cmslabel, 'pickup_only'); ?> (<?php echo $pickup_available_count; ?>): <?php echo (!empty($location_titles) ? $location_titles : ''); ?></span>
							<?php endif; ?>
						?> -->

						<template v-if="data.item.coupon_price"> <BaseCmsLabel code="coupon_value" />: <BaseUtilsFormatCurrency :price="data.item.coupon_price" /> </template>
						<template v-else-if="data.item.bonus_total"> <BaseCmsLabel code="bonus_total_value" />: <BaseUtilsFormatCurrency :price="data.item.bonus_total" /> </template>
						<template v-else-if="data.item.type && data.item.type == 'download' && mode != 'checkout'">
							<BaseCmsLabel code="download_files" />
						</template>

						<!-- <?php if($mode != 'checkout'): ?>
						<div class="wp-btns">
							<?php if(!empty($pickup_only)): ?>
							<a class="wp-btn wp-btn-delete" href="javascript:cmswebshop.shopping_cart.remove_all('<?php echo $product_code; ?>');"
								><span><?php echo Arr::get($cmslabel, 'remove_product', 'Obriši'); ?></span></a
							>
							<?php else: ?>
							<a class="wp-btn wp-btn-delete" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove');"
								><span><?php echo Arr::get($cmslabel, 'remove_product', 'Obriši'); ?></span></a
							>
							<?php endif; ?>
							<?php if (isset($product_data['wishlist_widget']) AND $product_data['wishlist_widget'] AND !$product_data['wishlist_widget']['active']): ?>
							<a
								class="wp-btn wp-wishlist wishlist_set_<?php echo $product_data['wishlist_widget']['content']; ?>"
								href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove');cmswishlist.set('<?php echo $product_data['wishlist_widget']['content']; ?>');"
								data-wishlist_operation="+|removelink"
								><?php echo Arr::get($cmslabel, 'move_to_wishlist'); ?></a
							>
							<?php endif; ?>
						</div>
						<?php endif; ?> -->

						<template v-if="mode != 'checkout'">
							<div class="wp-btns">
								<template v-if="data.item.type && data.item.type == 'pickup'">
									<!-- FIXME - potrebno je obrisati sve proizvode iz košarice kod pickupa! -->
									<div class="wp-btn wp-btn-delete" :class="{'loading': loading}" @click="onRemove"><BaseCmsLabel code="remove_product" tag="span" /></div>
								</template>
								<template v-else>
									<div class="wp-btn wp-btn-delete" :class="{'loading': loading}" @click="onRemove"><BaseCmsLabel code="remove_product" tag="span" /></div>
								</template>
								<template v-if="!wishlistItems.some(wishlistItem => wishlistItem.product_id === data.item.id)">
									<!-- FIXME - ovo ne radi kak spada piše da fali shopping_cart_code zato jer ne postoji pa automatski javlja error 400 (bad request) -->
									<ClientOnly>
										<CatalogSetWishlist :item="data.item" mode="cart" />
									</ClientOnly>
								</template>
							</div>
						</template>
					</div>

					<template v-if="mode != 'checkout'">
						<BaseWebshopQty :quantity="data.quantity" :item="data" mode="cart" v-slot="{loading, status, onDecrement, onIncrement, onUpdate, onReset, quantity}">
							<div class="wp-qty-container" :class="{'loading': loading}">
								<div class="wp-qty">
									<div class="wp-btn-qty wp-btn-dec" @click="onDecrement">-</div>
									<input class="wp-input-qty product_qty_input" type="text" :value="quantity" @keyup="onUpdate" @blur="onReset" />
									<div class="wp-btn-qty wp-btn-inc" @click="onIncrement">+</div>
									<div class="wp-unit" v-if="data?.unit">{{data.unit}}</div>
									<div class="wp-unit" v-else><BaseCmsLabel code="unit" /></div>
									<span v-show="status" class="wp-message product_message" :class="[{'product_message_response_error': status?.data?.label_name == 'error_limitqty' || status == 'error_product_maximum_qty'}, 'product_message_' + data.shopping_cart_code]"
										><BaseCmsLabel :code="status" />{{status == 'error_product_maximum_qty' ? ` ${quantity} kom` : ''}}</span
									>
								</div>
							</div>
						</BaseWebshopQty>
					</template>

					<!-- <div class="wp-total">
						<?php if (round($product_status['total_basic'], 2) > round($product_status['total'], 2)): ?>
							<div class="wp-old-price">
								<span class="product_total_basic" data-display_format="<?php echo Kohana::config('app.utils.kn_euro_conversion.display_format.none'); ?>" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span>
								<span class="product_total_basic_second" data-display_format="<?php echo Kohana::config('app.utils.kn_euro_conversion.display_format.none'); ?>" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
							</div>
							<div class="wp-discount-price">
								<span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
								<span class="product_total_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
							</div>
						<?php else: ?>
							<?php if (!empty($user->b2b)): ?>
								<div class="wp-current-price">
									<span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span>
									<span class="product_total_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
								</div>
							<?php else: ?>
								<?php /* 
									FIXME PROG na cijenu treba vjerojatno dodati neki data tag da se ispravno izračuna cijena 
									Scenarij:
									1. U incognito dodaš u košaricu proizvod http://tzh.markerdev.info/alfaalfa-lucerna-sjemenke-za-klijanje-organske-250g-nutrigold-proizvod-54145/
									2. U košarici označiš kvačicu za loyalty
									3. Cijena proizvoda se ažurira ali krivo. Ako se refreša s stranica, onda je ok https://snipboard.io/AeKDd7.jpg
								*/ ?>

								<?php if (!empty($shopping_cart['total_extra_loyalty']) AND !empty($product_status['total_items_loyalty'] AND $product_data['type'] != 'coupon')): ?>
									<div class="wp-old-price">
										<span class="product_total_basic" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></span>
										<span class="product_total_basic_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_basic'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
									</div>
									<div class="wp-discount-price">
										<span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total_items_loyalty'] * $currency['exchange'], $currency['display']); ?></span>
										<span class="product_total_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total_items_loyalty'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
									</div>
								<?php else: ?>
									<div class="wp-current-price">
										<span class="product_total" data-currency_format="full_price_currency" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
										<span class="product_total_second" data-currency_format="full_price_currency" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
									</div>
								<?php endif; ?>
							<?php endif; ?>
						<?php endif; ?>
						<?php $price = (!empty($shopping_cart['total_extra_loyalty']) AND !empty($product_status['loyalty_price']) AND $product_data['type'] != 'coupon') ? $product_status['loyalty_price'] : $product_status['price']; ?>

						<div class="wp-qty-count" data-shoppingcart_product_qty_box="<?php echo $product_code; ?>" <?php if ((float) $product_status['qty'] == 1): ?> style="display: none;"<?php endif; ?>>
							<?php if (!empty($product_status['related_item_price_qty'])): ?>
								<?php if ($product_status['related_item_price_qty'] == $product_status['qty']): ?>
									<span class="product_qty"><?php echo $product_status['qty']; ?></span> x 
									<?php echo Utils::currency_format($product_status['related_item_price'] * $currency['exchange'], $currency['display']); ?>
									<?php echo Utils::get_second_pricetag_string($product_status['related_item_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								<?php else: ?>
								<?php $qty = ($product_status['qty'] - $product_status['related_item_price_qty']); ?>
									<?php $price = ((!empty($shopping_cart['total_extra_loyalty']) AND !empty($product_status['loyalty_price'])) ? $product_status['loyalty_price'] : $product_status['price']); ?>
									<span class="product_qty_1"> <?php echo $product_status['related_item_price_qty']; ?></span> x <?php echo Utils::currency_format($product_status['related_item_price'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($product_status['related_item_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?> + <span class="product_qty_2"><?php echo $qty; ?></span> x <?php echo Utils::currency_format($price * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								<?php endif; ?>
							<?php else: ?>
								<span class="product_qty"><?php echo $product_status['qty']; ?></span> x 
								<?php echo Utils::currency_format($price * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							<?php endif; ?>
						</div>

						<?php if ($product_status['total_basic'] > $product_status['total']): ?>
							<?php if(!empty($product_status['extra_price_lowest']) AND $product_status['extra_price_lowest'] > 0): ?>
								<div class="wp-lowest-price">
									<?php echo Arr::get($cmslabel, 'lowest_price'); ?>:
									<?php echo Utils::currency_format($product_status['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
									<?php echo Utils::get_second_pricetag_string($product_status['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								</div>
							<?php endif; ?>
						<?php endif; ?>
					</div> -->

					<div class="wp-total">
						<template v-if="data.total_basic < data.total">
							<div v-if="data.total_basic > data.total" class="wp-old-price"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
							<div class="wp-current-price" :class="[{'wp-discount-price': data.total_basic > data.total}]"><BaseUtilsFormatCurrency :price="data.total" /></div>
							<div class="wwp-lowest-price ww-lowest-price" v-if="data.extra_price_lowest > 0 && data.total_basic > data.total">
								<BaseCmsLabel code="lowest_price" tag="span" />: <strong><BaseUtilsFormatCurrency :price="data.extra_price_lowest" /></strong>
							</div>
						</template>
						<template v-else>
							<template v-if="b2b">
								<div class="wp-current-price"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
							</template>
							<template v-else>
								<!-- FIXME INTEG složiti kad bude složen loyalty -->
								<WebshopLoyalty v-slot="{onSubmit, newIsActive, loading, loyalty}">
									<template v-if="data.type != 'coupon'">
										<div class="wp-current-price"><BaseUtilsFormatCurrency :price="data.total" /></div>
									</template>
									<template v-else>
										<div class="wp-current-price"><BaseUtilsFormatCurrency :price="data.total" /></div>
									</template>
								</WebshopLoyalty>
							</template>
						</template>

						<div class="wp-qty-count" v-if="data.quantity > 1">
							<!-- FIXME INTEG - ne dobivam podatak za 'related_item_price_qty'

							<?php if (!empty($product_status['related_item_price_qty'])): ?>
								<?php if ($product_status['related_item_price_qty'] == $product_status['qty']): ?>
									<span class="product_qty"><?php echo $product_status['qty']; ?></span> x 
									<?php echo Utils::currency_format($product_status['related_item_price'] * $currency['exchange'], $currency['display']); ?>
									<?php echo Utils::get_second_pricetag_string($product_status['related_item_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								<?php else: ?>
								<?php $qty = ($product_status['qty'] - $product_status['related_item_price_qty']); ?>
									<?php $price = ((!empty($shopping_cart['total_extra_loyalty']) AND !empty($product_status['loyalty_price'])) ? $product_status['loyalty_price'] : $product_status['price']); ?>
									<span class="product_qty_1"> <?php echo $product_status['related_item_price_qty']; ?></span> x <?php echo Utils::currency_format($product_status['related_item_price'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($product_status['related_item_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?> + <span class="product_qty_2"><?php echo $qty; ?></span> x <?php echo Utils::currency_format($price * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								<?php endif; ?>
							<?php else: ?>
								<span class="product_qty"><?php echo $product_status['qty']; ?></span> x 
								<?php echo Utils::currency_format($price * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							<?php endif; ?> -->
							<span>{{ data.quantity }} x </span><BaseUtilsFormatCurrency :price="data.unit_price" />
						</div>
						<!-- <div v-if="data.total_basic > data.total" class="wp-old-price"><BaseUtilsFormatCurrency :price="data.gross_amount * data.quantity" /></div>
						<div :class="['wp-current-price', {'wp-discount-price': data.total_basic > data.total}]"><BaseUtilsFormatCurrency :price="data.total" /></div>
 						-->
						<span class="wp-lowest-price" v-if="data.extra_price_lowest && data.discount_percentage > 0 && data.extra_price_lowest > 0">
							<BaseCmsLabel code="lowest_price" tag="span" />: <strong><BaseUtilsFormatCurrency :price="data.extra_price_lowest" /></strong>
						</span>
					</div>
				</div>
			</BaseCatalogWishlist>
		</BaseWebshopRemoveProduct>
	</div>
</template>

<script setup>
	const {b2b} = useProfile();
	/* function onLoadedProducts(products){
		if(products?.items.length){
			sendProductImpressions(products.items, "wishlist");
		}
	} */
	const props = defineProps(['data', 'item', 'mode']);
</script>
