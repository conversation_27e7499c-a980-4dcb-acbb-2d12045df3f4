<template>
	<div class="wwp ww-item">
		<div class="wwp-image ww-image">
			<NuxtLink :to="data.item.url_without_domain">
				<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width50-height50']" default="/images/no-image-50.jpg" :alt="data.item.title" />
			</NuxtLink>
		</div>

		<div class="wwp-cnt ww-cnt">
			<div class="wwp-title ww-title">
				<NuxtLink :to="data.item.url_without_domain">{{ data.item.title }}</NuxtLink>
			</div>
			<div class="df aic ww-bottom">
				<div class="ww-price">
					<template v-if="data.total_basic < data.total">
						<div v-if="data.total_basic > data.total" class="wwp-old-price ww-old-price"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
						<div class="wpw-price-current ww-current-price" :class="[{'wwp-price-discount ww-discount-price': data.total_basic > data.total}]"><BaseUtilsFormatCurrency :price="data.total" /></div>
						<div class="wwp-qty-count" v-if="data.quantity > 1">
							<span>{{ data.quantity }} x </span><BaseUtilsFormatCurrency :price="data.unit_price" />
						</div>
						<div class="wwp-lowest-price ww-lowest-price" v-if="data.extra_price_lowest > 0 && data.total_basic > data.total">
							<BaseCmsLabel code="lowest_price" tag="span" />: <strong><BaseUtilsFormatCurrency :price="data.extra_price_lowest" /></strong>
						</div>
					</template>
					<template v-else>
						<template v-if="b2b">
							<div class="ww-current-price"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
						</template>
						<template v-else>
							<!-- FIXME INTEG DK složiti kad bude složen loyalty -->
							<WebshopLoyalty v-slot="{onSubmit, newIsActive, loading, loyalty}">
								<template v-if="data.type != 'coupon'">
									<template v-if="loyalty?.active">
										<div class="ww-old-price">
											<span><BaseUtilsFormatCurrency :price="data.total_basic" /></span>
										</div>
										<div class="ww-current-price red" v-if="data.discount_percentage > loyalty.discount_percent">
											<span><BaseUtilsFormatCurrency :price="data.total" /></span>
										</div>

										<div class="ww-current-price red" v-else><BaseUtilsFormatCurrency :price="(data.total_basic * (1 - (loyalty.discount_percent  / 100)))" /></div>

										<div class="lowest-price ww-lowest-price">
											<template v-if="data.extra_price_lowest > 0">
												<BaseCmsLabel code="lowest_price" />: <strong><BaseUtilsFormatCurrency :price="data.extra_price_lowest" /></strong>
											</template>
										</div>
									</template>
									<template v-else>
										<div class="ww-old-price" v-if="data.total_basic > data.total">
											<span><BaseUtilsFormatCurrency :price="data.total_basic" /></span>
										</div>
										<div class="ww-current-price" :class="{'red': data.total_basic > data.total}">
											<span><BaseUtilsFormatCurrency :price="data.total" /></span>
										</div>
									</template>
								</template>
								<template v-else>
									<div class="ww-current-price"><BaseUtilsFormatCurrency :price="data.total" /></div>
								</template>
							</WebshopLoyalty>
						</template>
					</template>
				</div>
				<BaseWebshopRemoveProduct :item="data" v-slot="{onRemove, loading}" v-if="mode != 'checkout'">
					<span class="wp-btn ww-btn-delete" :class="{'loading': loading}" @click="onRemove"></span>
				</BaseWebshopRemoveProduct>

				<!-- FIXME INTEG DK provjeriti package_qty -->
				<div class="ww-qty-container">
					<BaseThemeWebshopQty :quantity="data.quantity" :limit="data.available_quantity" :item="data" mode="cart" />
				</div>
				<!--
				<div class="ww-qty-container">
					<div class="wp-qty ww-qty">
						<?php $package_qty = (!empty($product_data['package_qty'])) ? $product_data['package_qty'] : 1; ?>
						<a class="wp-btn-qty wp-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '-', 5, 1, 0, 0, <?php echo $package_qty ?>);"><span class="toggle-icon"></span></a>
						<input class="wp-input-qty product_qty_input" type="text" name="qty_webshop_preview[<?php echo $product_code; ?>]" value="<?php echo $product_status['qty']; ?>" />
						<a class="wp-btn-qty wp-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '+', 5, 1, 0, 0, <?php echo $package_qty ?>);"><span class="toggle-icon"></span></a>
						<span class="wp-message ww-message product_message product_message_<?php echo $product_code; ?>" style="display: none"></span>
					</div>
				</div>

				-->
			</div>
			<!--<div class="wwp-code"><BaseCmsLabel code="code" />: {{ (data.item.variation?.code) ? data.item.variation.code : data.item.code }}</div>-->
		</div>
		<!--
		<div class="wwp-price">
			<div :class="['wwp-current-price', {'red': data.total_basic > data.total}]"><BaseUtilsFormatCurrency :price="data.total" /></div>
			<div class="wwp-qty-count" v-if="data.quantity > 1">
				<span>{{ data.quantity }} x </span><BaseUtilsFormatCurrency :price="data.unit_price" />
			</div>
		</div>
		-->
	</div>
</template>

<script setup>
	const props = defineProps(['data', 'mode']);
	const {b2b} = useProfile();
</script>
