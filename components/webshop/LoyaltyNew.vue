<template>
	<WebshopLoyalty v-slot="{onSubmit, loading, newIsActive, newCartTotal, newCartPercent}">
		<div v-if="newCartTotal && newCartPercent" class="w-loyalty" :class="{'checkout-new-loyalty': mode == 'checkout'}">
			<div class="loyalty-cnt" v-if="!mobileBreakpoint">
				<div v-html="labels.get('join_loyalty').replace('%AMOUNT%', `<span class='value cart_info_total_extra_loyalty_new'>${formatCurrency(newCartTotal)}</span>`)"></div>
			</div>
			<div class="loyalty-checkbox" :class="{'loading': loading}">
				<input type="checkbox" @click.prevent="onSubmit()" name="loyalty_request_new" id="field-loyalty_request_new" :checked="newIsActive" />
				<label
					for="field-loyalty_request_new"
					v-html="mobileBreakpoint ? labels.get('join_loyalty_label_new').replace('%AMOUNT%', `<span class='value cart_info_total_extra_loyalty_new'>${formatCurrency(newCartTotal)}</span>`) : labels.get('join_loyalty_label').replace('%AMOUNT%', `<span class='value cart_info_total_extra_loyalty_new'>${formatCurrency(newCartTotal)}</span>`)"></label>
			</div>
		</div>
	</WebshopLoyalty>
</template>

<script setup>
	const {mobileBreakpoint} = inject('rwd');
	const props = defineProps(['mode']);
	const labels = useLabels();

	const {formatCurrency} = useCurrency();
</script>
