<template>
	<BaseCmsPage>
		<Body class="page-catalog-view_wishlist page-catalog-view_wishlist page-wishlist white-bg" />
		<ClientOnly>
			<BaseCatalogWishlist v-slot="{items,  onRemove}" @loadProductsWidget="onLoadedProducts">
				<div class="wrapper wrapper-wishlist">
					<div v-if="items?.length > 0" id="view_wishlist">
						<div class="c-items c-items5 wishlist-items">
							<template v-for="item in items" :key="item.id">
								<CatalogIndexEntry :item="item" mode="wishlist" />
							</template>
						</div>
						<div class="wishlist-btns" v-if="items?.length > 5">
							<div @click="onRemove" class="btn btn-white btn-wishlist-delete">
								<BaseCmsLabel tag="span" code="wishlist_delete" />
							</div>
						</div>
					</div>
					<div v-else class="c-empty wishlist-empty">
						<BaseCmsLabel code="wishlist_no_products" />
					</div>
				</div>
			</BaseCatalogWishlist>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const auth = useAuth();
	const labels = useLabels();
	const {getAppUrl} = useApiRoutes();
	const isLoggedIn = computed(() => auth.isLoggedIn());

	onMounted(() => {
		if(isLoggedIn.value) {
			navigateTo({path: getAppUrl('auth_wishlist')})
		}
	})

	function onLoadedProducts(products){
		if(products?.items.length){
			sendProductImpressions(products.items, "wishlist");
		}
	}
</script>
