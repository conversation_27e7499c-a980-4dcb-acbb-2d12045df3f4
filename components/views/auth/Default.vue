<template>
	<Body class="page-auth-index main-offset-sm page-auth" />
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseAuthUser v-slot="{user, urls, currentUrl}">
				<AuthMainLayout>
					<template #authContent>
						<h1 class="a-title" v-if="page?.title">{{page.title}}</h1>
						<BaseAuthEditForm class="form-label ajax_siteform_loading" v-slot="{fields, loading, status}">
							<template v-if="status?.data?.errors?.length">
								<div class="global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
							</template>
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
								<template v-if="item.name == 'birthday'">
									<div class="auth-default-birthday">
										<BaseCmsLabel class="birthday-note" tag="span" code="auth_birthday_note" />
										<div class="field-birthday-cnt">
											<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
												<!-- FIXME - ne sprema mi se birthday value kad ga odaberem u DatePickeru i spremim -->
												<UiDatePicker :minDate="'01.01.1900'" :maxDate="new Date().toLocaleDateString('hr').replace(/\s+/g, '')" :uid="item.name" v-slot="{selectedDate}">
													<BaseFormInput hidden readonly :value="selectedDate ? selectedDate.toLocaleDateString('hr').replace(/\s+/g, '') : ''" />
													<label :for="`dp-input-${item.name}`" class="label-datepicker"><BaseCmsLabel code="auth_birthday2" /><span v-if="item.validation?.length && item.validation.some(item => item.type === 'not_empty')"> *</span></label>
												</UiDatePicker>
												<span class="error" v-show="errorMessage" v-html="errorMessage" />
											</p>
										</div>
										<span class="error" v-show="errorMessage" v-html="errorMessage" />
										<button class="btn btn-birthday-save" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="save2" /></button>
									</div>
								</template>
							</BaseFormField>
						</BaseAuthEditForm>
						<div class="df a-intro">
							<div class="fg1 a-intro-left">
								<BaseCmsLabel code="you_can" class="a-intro-title" tag="p" />
								<ul class="a-menu" v-interpolation>
									<!-- <NuxtLink :to="urls.auth_my_webshoporder" :class="{'active': urls.auth_my_webshoporder == currentUrl}"><BaseCmsLabel code="auth_view_orders" tag="span" /></NuxtLink> -->
									<li>
										<BaseCmsLabel tag="span" code="auth_view_orders" :replace="[{'%LINK%': urls.auth_my_webshoporder}]" />
									</li>
									<li>
										<!-- <NuxtLink :to="urls.auth_wishlist" :class="{'active': urls.auth_wishlist == currentUrl}"><BaseCmsLabel code="auth_view_wishlist" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_view_wishlist" :replace="[{'%LINK%': urls.auth_wishlist}]" />
									</li>
									<li v-if="user.b2b == 0">
										<!-- <NuxtLink :to="urls.auth+'#coupons'" :class="{'active': urls.auth_my_webshopcoupon == currentUrl}"><BaseCmsLabel code="auth_view_coupons" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_view_coupons" :replace="[{'%LINK%': urls.auth_my_webshopcoupon}]" />
									</li>
									<li>
										<!-- <NuxtLink :to="urls.auth_edit" :class="{'active': urls.auth_edit == currentUrl}"><BaseCmsLabel code="auth_edit_profile" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_edit_profile" :replace="[{'%LINK%': urls.auth_edit}]" />
									</li>
									<li>
										<!-- <NuxtLink :to="urls.auth_change_password" :class="{'active': urls.auth_change_password == currentUrl}"><BaseCmsLabel code="auth_change_password" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_change_password" :replace="[{'%LINK%': urls.auth_change_password}]" />
									</li>
									<li>
										<!-- <NuxtLink :to="urls.auth_logout+'?redirect=/'"><BaseCmsLabel code="auth_logout" tag="span" /></NuxtLink> -->
										<BaseCmsLabel tag="span" code="auth_logout" :replace="[{'%LINK%': urls.auth_logout+'?redirect=/'}]" />
									</li>
								</ul>
							</div>
							<div class="a-intro-user">
								<div class="a-intro-title" v-if="user?.first_name && user?.last_name">{{user.first_name}} {{user.last_name}}</div>
								<p>
									<span class="a-intro-email" v-if="user?.email">{{user.email}}</span
									><br />
									<span class="a-intro-tel" v-if="user?.phone">{{user.phone}}</span>
								</p>
								<template v-if="user?.address">
									<p>
										{{user.address}}<br />
										{{user.zipcode}}
										{{user.city}}
									</p>
								</template>
								<NuxtLink class="btn btn-gray btn-auth-edit" :to="urls.auth_edit" :class="{'active': urls.auth_edit == currentUrl}"><BaseCmsLabel code="edit_profile" /></NuxtLink>
							</div>
						</div>
						<BaseAuthOrders v-slot="{items: orders}">
							<template v-if="orders?.length">
								<div class="auth-box auth-box-orders orders-container" id="orders">
									<div class="a-section-title a-section-orders-title">
										<NuxtLink :to="urls.auth_my_webshoporder"><BaseCmsLabel code="latest_orders" tag="span" /></NuxtLink>
									</div>
									<AuthWebshopOrders :orders="orders" mode="dashboard" />
								</div>
								<div class="auth-box-btns">
									<NuxtLink class="btn" :to="urls.auth_my_webshoporder"><BaseCmsLabel code="webshop_orders_show_all" tag="span" /></NuxtLink>
								</div>
							</template>
						</BaseAuthOrders>
					</template>
				</AuthMainLayout>
			</BaseAuthUser>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<style lang="less" scoped>
	:deep(.field){
		[hidden]{display: none;}
	}
	:deep(.dp__clear_icon){
		right: 50px;
	}
	:deep(.dp__input){
		border: unset; background: white; border-radius: @borderRadius; padding: 0 25px 0 52px; border-radius: 0; background: url(assets/images/icons/calendar-black.svg) #FFF no-repeat center left +16px;
	}
	:deep(.dp__clear_icon){
		right: 5px;
	}

	.auth-default-birthday{
		width: 100%; background: linear-gradient(180deg, #ABC075 0%, #809941 100%); border-radius: @borderRadius; padding: 16px 16px 16px 136px; color: white; display: flex; align-content: center; margin-bottom: 32px; position: relative;
		&:before{.pseudo(auto,auto); color: white; .icon-surprise; font: 88px/88px @fonti; left: 24px; top: 8px;}
	}
	.form-label{
		label{color: @textColor; font-size: 16px; left: 52px; pointer-events: none;}
	}
	.form-label .focus label, .form-label .ffl-floated label{
		font-size: 12px;
	}
	.birthday-note{max-width: 338px; font-size: 20px; line-height: 1.3; margin-right: 38px;}
	.field-birthday-cnt{display: flex; align-items: center;}
	.field-birthday{padding-bottom: 0; flex-grow: 1;}
	:deep(.btn-birthday-save){
		background: #244538; color: white; font-weight: bold; padding: 0 28px; flex-shrink: 0; border-radius: 0 3px 3px 0; .transition(background);
		&:hover{background: #1f3b2f; color: white;}
		&:after{display: none !important;}
	}
</style>
