<template>
	<Body class="page-publish-index page-recipes" />
	<BasePublishCategory :root-category="true" :include-subcategories="true" v-slot="{item: category, rootCategory}" :seo="true">
		<BasePublishPosts v-slot="{items: posts, nextPage, loadMore, loading}">
			<div class="wrapper">
				<div class="df p-recipes-row">
					<!-- FIXME - potrebno vidjeti kakva su tu neka preračunavanja vezana za hellobar jednom kad se hellobar napravi
					
					<?php $hello_bar_rotator = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'hello_bar', 'limit' => 1, 'ignore_hidden_element' => true]); ?>
					<?php $hello_bar_rotator_hidden = false; ?>
					<?php if(!empty($hello_bar_rotator)): ?>
						<?php foreach ($hello_bar_rotator as $hello_bar): ?>
							<?php if($hello_bar['coupon_code'] != null AND !empty($user->b2b)) : ?>
								<?php $hello_bar_rotator_hidden = true; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php endif; ?>

					<aside class="p-recipes-sidebar<?php if (!empty($hello_bar_rotator)): ?> recipes-sidebar-hellobar<?php endif; ?><?php if($hello_bar_rotator_hidden): ?> hello-bar-none<?php endif; ?>">
						<?php $search_fields = Widget_Publish::search_filters(array('lang' => $info['lang'], 'filters' => $filters, 'search_id' => ((isset($kind['search_id']) AND $kind['search_id']) ? $kind['search_id'] : 1))); ?>
						<?php $active_filters = (!empty($search_fields['_basic']['selected'])) ? $search_fields['_basic']['selected'] : []; ?>
						<?php if (count($items) > 0 OR $active_filters): ?>
							<?php echo View::factory('catalog/widget/filter', ['search_fields' => $search_fields, 'active_filters' => $active_filters, 'class' => 'cf-recipes', 'mode' => 'recipes']); ?>
						<?php endif; ?>
					</aside> -->

					<aside class="p-recipes-sidebar">
						<!-- FIXME - potrebno složiti filtere za publish recepte
						
						<?php $search_fields = Widget_Publish::search_filters(array('lang' => $info['lang'], 'filters' => $filters, 'search_id' => ((isset($kind['search_id']) AND $kind['search_id']) ? $kind['search_id'] : 1))); ?>
						<?php $active_filters = (!empty($search_fields['_basic']['selected'])) ? $search_fields['_basic']['selected'] : []; ?>
						<?php if (count($items) > 0 OR $active_filters): ?>
							<?php echo View::factory('catalog/widget/filter', ['search_fields' => $search_fields, 'active_filters' => $active_filters, 'class' => 'cf-recipes', 'mode' => 'recipes']); ?>
						<?php endif; ?> -->

						<BasePublishActiveFilters v-slot="{items, onRemove}">
							<template v-if="items?.length">
								<div v-for="item in items" :key="item.id" @click="onRemove(item)">X {{item.title}}</div>
							</template>
						</BasePublishActiveFilters>

						<BasePublishFilters v-slot="{searchFields, selectedFiltersCounter}">
							Selected filters: {{selectedFiltersCounter}}
							<div class="cf">
								<BasePublishFilterItem v-for="filter in searchFields" :key="filter.id" :item="filter" v-slot="{fields, totalFields, onFilter, onClear, selectedFilters, active}">
									<div class="cf-item">
										<div class="cf-title">{{filter.label}}</div>
										<div class="cf-item-wrapper">
											<div class="cf-row" v-for="field in fields" :key="field.id">
												<input type="checkbox" :name="filter.filter_url" :id="field.unique_code" :value="field.filter_url" :checked="field.selected" @click="onFilter" />
												<label :for="field.unique_code">
													{{field.title}}
													<span>({{ field.total_available }})</span>
												</label>
											</div>
										</div>
									</div>
									<div class="cf-clear" @click="onClear">Clear</div>
									<hr />
								</BasePublishFilterItem>
							</div>

							<!--
							<BasePublishActiveFilters v-slot="{items: activeFilter}">
								<BasePublishFilterItem v-for="filter in searchFields" :key="filter.id" :item="filter" />
							</BasePublishActiveFilters>
							-->
						</BasePublishFilters>
					</aside>

					<div class="p-recipes-main" v-if="posts?.length">
						<div class="p-items">
							<PublishIndexEntryRecipes v-for="post in posts" :key="post.id" :item="post" />
						</div>
						<ClientOnly>
							<div class="load-more-container" v-if="nextPage" data-posts-scroll-trigger>
								<button type="button" class="btn load-more btn-load-more btn-load-more-recipes" :class="{'loading': loading}" @click="loadMore"><UiLoader v-if="loading" size="small" color="white" /><BaseCmsLabel tag="span" code="load_more_recipes" /></button>
							</div>
						</ClientOnly>
						<BaseUiPagination class="pagination" />
					</div>
					<div v-else class="p-empty"><BaseCmsLabel code="no_publish" /></div>
				</div>
			</div>
		</BasePublishPosts>
	</BasePublishCategory>
</template>
