<template>
	<!-- FIXME INTEG složiti ga4 : <?php $ga4_data = Google4::create_event('view_item_list', ['cart_info' => $shopping_cart, 'items' => $bestseller_items, 'item_list_name' => Arr::get($cmslabel, 'catalog_special_title'), 'item_list_id' => 'catalog_special_title']); ?> -->
	<div class="c-special" v-if="items?.length">
		<div class="c-special-title"><BaseCmsLabel code="catalog_special_title" tag="span" /></div>
		<div class="c-special-items-slider">
			<div class="c-items c-items5 c-special-items blazy-container">
				<template v-for="relatedItem in items" :key="relatedItem.id">
					<CatalogIndexEntry :item="relatedItem" itemListId="catalog_special_title" :itemListName="labels.get('catalog_special_title')" />
				</template>
			</div>
		</div>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['items', 'categoryPosition']);
</script>

<style lang="less" scoped>
	.c-special{position: relative;}
	.c-special-title{
		position: absolute; top: 0; left: 0; font-size: 36px; line-height: 42px; font-weight: 700; color: #244538;
		span{position: absolute; top: 143px; right: -114px; .rotate(-90deg); white-space: nowrap;}
	}
</style>
