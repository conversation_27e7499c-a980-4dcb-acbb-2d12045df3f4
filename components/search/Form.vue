<template>
	<BaseSearchForm :fetch="searchFormConfig" v-slot="{searchResults, updateValue, totalSearchResults, searchTerm, loading, handleInput, onReset, selectedIndex, onSubmit}" ref="searchForm">
		<div class="sw" :class="[{'loading': loading}, {'active': searchTerm}, {'active': active}]" ref="sw">
			<div class="sw-toggle" v-if="tabletBreakpoint" @click="open()"></div>
			<div class="sw-form" id="main_search" method="get" :class="[{'active': active}]">
				<div class="sw-input-container">
					<div class="sw-clear" v-show="searchTerm" @click="onReset" v-if="!tabletBreakpoint"></div>
					<div class="sw-clear 2" @click="close()" v-if="tabletBreakpoint"></div>
					<input ref="searchInput" class="sw-input" name="search_q" id="search_q" type="text" :placeholder="labels.get('enter_search_term')" :value="searchTerm" @input="updateValue" autocomplete="off" @keyup="handleInput" />
					<button @click="onSubmit()" class="sw-btn" type="submit"><BaseCmsLabel code="search_button" /></button>

					<ClientOnly>
						<LazySearchAutocomplete v-if="totalSearchResults || loading" :searchResults="searchResults" :totalSearchResults="totalSearchResults" :loading="loading" :selectedIndex="selectedIndex" :onReset="onReset" :searchTerm="searchTerm" />
					</ClientOnly>
				</div>
			</div>
		</div>
	</BaseSearchForm>
</template>

<script setup>
	const labels = useLabels();
	const {onClickOutside} = useDom();
	
	const {tabletBreakpoint} = inject('rwd');

	const active = ref(false);

	const searchFormConfig = {
		'allow_models': ['catalogproduct', 'catalogcategory', 'publish.01', 'publish.02', 'catalogmanufacturer'],
		'result_fields': {
			'catalogproduct': ['image', 'price_custom', 'category_title', 'code', 'discount_percent_custom'],
		},
		'result_per_page': {
			'catalogproduct': 5,
			'catalogcategory': 4,
			'catalogmanufacturer': 4,
			'publish.01': 4,
			'publish.02': 4,
		},
		'result_image': '60x60_r'
	}

	const sw = ref(null);
	const searchForm = ref(null);
	const searchInput = ref(null);

	function open() {
		active.value = true;
		setTimeout(()	=> {
			searchInput.value.focus();
		}, 100);
	}

	function close() {
		searchForm.value.onReset();
		active.value = false;
	}

	onClickOutside(sw, () => {
		searchForm.value.resetAutocomplete();
		active.value = false;
		searchForm.value.onReset();
	});
</script>

<style lang="less" scoped>
	.sw-input-container{position: relative; display: flex; height: 100%;}	
</style>