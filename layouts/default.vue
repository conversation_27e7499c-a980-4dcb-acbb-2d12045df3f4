<template>
	<Body :class="{'fixed-header': y > 270, 'active-nav': activeNav}" />
	<div class="page-wrapper">
		<CmsHeader>
			<template #navBtn>
				<div class="btn-toggle-nav" :class="{'active': activeNav}" v-if="mobileBreakpoint" @click="activeNav = !activeNav"><span></span></div>
			</template>

			<template #afterHeader>
				<CmsHomepageIntro v-if="isHomepage" />
				<CatalogIndexHeader v-if="showCategoriesWidget" />
				<CatalogCategoriesWidget :extraclass="'categories c-categories'" v-if="showCategoriesWidget" />
				<PublishPostHeader v-if="isPublishDetail" />
				<PublishIndexHeader v-if="isPublishIndex" />
				<CatalogIndexWishlistHeader v-if="isIndexWishlistHeader" />
				<WebshopCartHeader v-if="isWebshopCartHeader" />
			</template>
		</CmsHeader>

		<slot />

		<CmsLayoutElements :instashopData="instashopData" />
	</div>
</template>

<script setup>
	const route = useRoute();
	const {onMediaQuery} = useDom();
	const {matches: tabletBreakpoint} = onMediaQuery({query: '(max-width: 1250px)'});
	const {matches: tabletSmallBreakpoint} = onMediaQuery({query: '(max-width: 1030px)'});
	const {matches: mobileBreakpoint} = onMediaQuery({query: '(max-width: 990px)'});
	const {matches: mobileBreakpointInstashop} = onMediaQuery({query: '(max-width: 800px)'});
	provide('rwd', {mobileBreakpoint, tabletBreakpoint, tabletSmallBreakpoint, mobileBreakpointInstashop});
	const {onScroll, scrollTo} = useDom();

	const instashopData = useState('instashopData');

	// Workaround to move publish detail header to header slot
	const isPublishDetail = computed(() => {
		return route.meta.template === 'PublishDetail' || route.meta.template === 'PublishRecipesDetail' ? true : false;
	});

	// Workaround to move recipes index header to header slot
	const isPublishIndex = computed(() => {
		return route.meta.template === 'PublishRecipes' || route.meta.template === 'PublishAuthorDetail' ? true : false;
	});

	// Workaround to move wishlist header to header slot
	const isIndexWishlistHeader = computed(() => {
		return route.meta.template === 'CatalogWishlist' ? true : false;
	});

	// Workaround to move cart header to header slot
	const isWebshopCartHeader = computed(() => {
		return route.meta.template === 'WebshopShoppingCart' ? true : false;
	});

	const showCategoriesWidget = computed(() => {
		return route.meta.controller == 'catalog' && route.meta.contentType == 'category' && route.params.slug.length == 2 ? true : false;
	});

	const isHomepage = computed(() => {
		return route.meta.template === 'CmsHomepage' ? true : false;
	});

	const activeNav = ref(false);
	const {y} = onScroll({
		debounce: 150,
	});

	watch(
		() => route.fullPath,
		(newPath, oldPath) => {
			activeNav.value = false;
		}
	);
</script>
